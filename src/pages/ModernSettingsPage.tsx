import { useState, useEffect } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { useTheme } from "@/hooks/use-theme";
import { useToast } from "@/components/ui/use-toast";
import {
  Moon, Sun, Trash2, Mail, User, ArrowLeft, Shield, Activity,
  Palette, Link as LinkIcon, X, ChevronRight, Timer, Bell, AlarmClock,
  Share2, Copy, MessageSquare, ExternalLink, Sparkles, Settings,
  Heart, Star, Zap, Globe, Lock, Eye, EyeOff
} from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  getUserProfile,
  updateUserProfile,
  checkUsernameAvailability,
  getStudySessions,
  linkWithEmailPassword,
  linkWithGoogle,
  unlinkProvider,
  deleteAllChats,
  getExtendedUserProfile
} from "@/integrations/supabase/client";

// Interfaces
interface UserStats {
  totalSessions: number;
  totalMinutes: number;
  averageSessionLength: number;
  longestStreak: number;
  currentStreak: number;
  timerSettings?: TimerSettings;
}

interface TimerSettings {
  workDuration: number;
  shortBreakDuration: number;
  longBreakDuration: number;
  sessionsUntilLongBreak: number;
  notificationInterval: number;
  dayResetHour: number;
}

interface ProductivityVisibilitySettings {
  showQuotes: boolean;
  showTasks: boolean;
  showExamCountdown: boolean;
  showSpotifyBar: boolean;
  spotifyCollapsed: boolean;
}

// Default settings
const DEFAULT_TIMER_SETTINGS: TimerSettings = {
  workDuration: 25 * 60,
  shortBreakDuration: 5 * 60,
  longBreakDuration: 15 * 60,
  sessionsUntilLongBreak: 4,
  notificationInterval: 60,
  dayResetHour: 4,
};

const DEFAULT_VISIBILITY_SETTINGS: ProductivityVisibilitySettings = {
  showQuotes: true,
  showTasks: true,
  showExamCountdown: true,
  showSpotifyBar: true,
  spotifyCollapsed: false
};

const ModernSettingsPage = () => {
  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  const [username, setUsername] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();
  const { theme, toggleTheme } = useTheme();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [providers, setProviders] = useState<string[]>([]);
  const [linkingPassword, setLinkingPassword] = useState("");
  const [linkingLoading, setLinkingLoading] = useState(false);
  const [linkingError, setLinkingError] = useState("");
  const [showExistingEmailForm, setShowExistingEmailForm] = useState(false);
  const [existingEmail, setExistingEmail] = useState("");
  const [existingPassword, setExistingPassword] = useState("");
  const [timerSettings, setTimerSettings] = useState<TimerSettings>(DEFAULT_TIMER_SETTINGS);
  const [visibilitySettings, setVisibilitySettings] = useState<ProductivityVisibilitySettings>(DEFAULT_VISIBILITY_SETTINGS);
  const [notificationPermission, setNotificationPermission] = useState<"default" | "granted" | "denied">("default");

  // Load user data and settings
  useEffect(() => {
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission as "default" | "granted" | "denied");
    }

    const loadUserData = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        const userData = await getExtendedUserProfile(user.id);
        if (userData) {
          setUsername(userData.username || "");
          setStats(userData.stats || null);
          setProviders(userData.providers || []);

          if (userData.progress?.productivitySettings) {
            setVisibilitySettings({
              showQuotes: userData.progress.productivitySettings.showQuotes ?? DEFAULT_VISIBILITY_SETTINGS.showQuotes,
              showTasks: userData.progress.productivitySettings.showTasks ?? DEFAULT_VISIBILITY_SETTINGS.showTasks,
              showExamCountdown: userData.progress.productivitySettings.showExamCountdown ?? DEFAULT_VISIBILITY_SETTINGS.showExamCountdown,
              showSpotifyBar: userData.progress.productivitySettings.showSpotifyBar ?? DEFAULT_VISIBILITY_SETTINGS.showSpotifyBar,
              spotifyCollapsed: userData.progress.productivitySettings.spotifyCollapsed ?? DEFAULT_VISIBILITY_SETTINGS.spotifyCollapsed
            });
          }

          if (userData.stats?.timerSettings) {
            const userTimerSettings = {
              workDuration: userData.stats.timerSettings.workDuration ?? DEFAULT_TIMER_SETTINGS.workDuration,
              shortBreakDuration: userData.stats.timerSettings.shortBreakDuration ?? DEFAULT_TIMER_SETTINGS.shortBreakDuration,
              longBreakDuration: userData.stats.timerSettings.longBreakDuration ?? DEFAULT_TIMER_SETTINGS.longBreakDuration,
              sessionsUntilLongBreak: userData.stats.timerSettings.sessionsUntilLongBreak ?? DEFAULT_TIMER_SETTINGS.sessionsUntilLongBreak,
              notificationInterval: userData.stats.timerSettings.notificationInterval ?? DEFAULT_TIMER_SETTINGS.notificationInterval,
              dayResetHour: userData.stats.timerSettings.dayResetHour ?? DEFAULT_TIMER_SETTINGS.dayResetHour
            };
            setTimerSettings(userTimerSettings);
            localStorage.setItem('timerSettings', JSON.stringify(userTimerSettings));
          } else {
            const storedSettings = localStorage.getItem('timerSettings');
            if (storedSettings) {
              try {
                const parsedSettings = JSON.parse(storedSettings);
                setTimerSettings(parsedSettings);
              } catch (e) {
                console.error('Error parsing stored timer settings:', e);
                setTimerSettings(DEFAULT_TIMER_SETTINGS);
              }
            }
          }
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        toast({
          title: "Error",
          description: "Failed to load user data.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, [user, toast]);

  // Handler functions
  const handleUsernameUpdate = async () => {
    if (!user || !username.trim()) return;

    setIsUpdating(true);
    try {
      const isAvailable = await checkUsernameAvailability(username.trim());
      if (!isAvailable) {
        toast({
          title: "Username Taken",
          description: "This username is already taken. Please choose another.",
          variant: "destructive",
        });
        return;
      }

      await updateUserProfile(user.id, { username: username.trim() });
      toast({
        title: "Username Updated",
        description: "Your username has been successfully updated.",
      });
    } catch (error) {
      console.error('Error updating username:', error);
      toast({
        title: "Error",
        description: "Failed to update username.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const updateTimerSettings = async (updates: Partial<TimerSettings>) => {
    if (!user) return;

    const updatedSettings = { ...timerSettings, ...updates };
    setTimerSettings(updatedSettings);
    localStorage.setItem('timerSettings', JSON.stringify(updatedSettings));

    try {
      const userData = await getExtendedUserProfile(user.id);
      const currentStats = userData?.stats || {};

      await updateUserProfile(user.id, {
        stats: {
          ...currentStats,
          timerSettings: updatedSettings
        }
      });

      toast({
        title: "Settings Updated",
        description: "Your timer settings have been saved.",
      });
    } catch (error) {
      console.error('Error saving timer settings:', error);
      toast({
        title: "Error",
        description: "Failed to save timer settings.",
        variant: "destructive",
      });
    }
  };

  const updateVisibilitySettings = async (key: keyof ProductivityVisibilitySettings, value: boolean) => {
    if (!user) return;

    const newSettings = { ...visibilitySettings, [key]: value };
    setVisibilitySettings(newSettings);

    if (key === 'showSpotifyBar') {
      localStorage.setItem('spotify-is-visible', JSON.stringify(value));
    } else if (key === 'spotifyCollapsed') {
      localStorage.setItem('spotify-is-collapsed', JSON.stringify(value));
    }

    try {
      const userData = await getExtendedUserProfile(user.id);
      const currentProgress = userData?.progress || {};

      await updateUserProfile(user.id, {
        progress: {
          ...currentProgress,
          productivitySettings: newSettings
        }
      });

      toast({
        title: "Settings Updated",
        description: "Your productivity settings have been saved.",
      });
    } catch (error) {
      console.error('Error saving visibility settings:', error);
      toast({
        title: "Error",
        description: "Failed to save settings.",
        variant: "destructive",
      });
    }
  };

  const requestNotificationPermission = async () => {
    if (!('Notification' in window)) {
      toast({
        title: "Not Supported",
        description: "Notifications are not supported in this browser.",
        variant: "destructive",
      });
      return;
    }

    try {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission as "default" | "granted" | "denied");

      if (permission === 'granted') {
        toast({
          title: "Notifications Enabled",
          description: "You'll now receive timer notifications.",
        });
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  };

  const handleLinkWithEmail = async () => {
    if (!user || !linkingPassword) return;

    setLinkingLoading(true);
    setLinkingError("");

    try {
      await linkWithEmailPassword(user.email!, linkingPassword);
      setProviders([...providers, 'password']);
      setLinkingPassword("");
      toast({
        title: "Account Linked",
        description: "Email and password login has been added to your account.",
      });
    } catch (error: any) {
      console.error('Error linking with email:', error);
      if (error.message?.includes('email-already-in-use')) {
        setShowExistingEmailForm(true);
        setLinkingError("This email is already registered. Please sign in with your existing account to merge.");
      } else {
        setLinkingError(error.message || "Failed to link account. Please try again.");
      }
    } finally {
      setLinkingLoading(false);
    }
  };

  const handleLinkWithGoogle = async () => {
    if (!user) return;

    setLinkingLoading(true);
    setLinkingError("");

    try {
      await linkWithGoogle();
      setProviders([...providers, 'google.com']);
      toast({
        title: "Account Linked",
        description: "Google login has been added to your account.",
      });
    } catch (error: any) {
      console.error('Error linking with Google:', error);
      setLinkingError(error.message || "Failed to link Google account. Please try again.");
    } finally {
      setLinkingLoading(false);
    }
  };

  const handleUnlinkProvider = async (providerId: string) => {
    if (!user || providers.length <= 1) return;

    setLinkingLoading(true);
    try {
      await unlinkProvider(providerId);
      setProviders(providers.filter(p => p !== providerId));
      toast({
        title: "Account Unlinked",
        description: "Login method has been removed from your account.",
      });
    } catch (error: any) {
      console.error('Error unlinking provider:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to unlink account.",
        variant: "destructive",
      });
    } finally {
      setLinkingLoading(false);
    }
  };

  const handleCancelLinkExisting = () => {
    setShowExistingEmailForm(false);
    setExistingEmail("");
    setExistingPassword("");
    setLinkingError("");
  };

  const handleDeleteChats = async () => {
    if (!user) return;

    setIsUpdating(true);
    try {
      await deleteAllChats(user.id);
      toast({
        title: "Chats Deleted",
        description: "All your chat conversations have been permanently deleted.",
      });
    } catch (error) {
      console.error('Error deleting chats:', error);
      toast({
        title: "Error",
        description: "Failed to delete chat conversations.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <>
      {/* Custom Animations */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
          50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.5); }
        }
        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        .animate-float { animation: float 3s ease-in-out infinite; }
        .animate-glow { animation: glow 2s ease-in-out infinite; }
        .animate-spin-slow { animation: spin-slow 8s linear infinite; }
        .card-hover {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .card-hover:hover {
          transform: translateY(-4px);
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
      `}</style>

      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 dark:from-slate-950 dark:via-slate-900 dark:to-indigo-950/20 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-indigo-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-green-400/10 to-emerald-400/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Modern Header */}
      <div className="sticky top-0 z-50 backdrop-blur-xl bg-white/80 dark:bg-slate-950/80 border-b border-slate-200/50 dark:border-slate-800/50 shadow-lg">
        <div className="container max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate(-1)}
              className="rounded-full hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-300 hover:scale-105 group"
            >
              <ArrowLeft className="h-5 w-5 group-hover:-translate-x-0.5 transition-transform duration-200" />
            </Button>
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <Settings className="h-6 w-6 text-white animate-spin-slow" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                  Settings
                </h1>
                <p className="text-sm text-slate-500 dark:text-slate-400">Customize your IsotopeAI experience</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <Tabs defaultValue="profile" className="w-full">
          <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
            {/* Modern Sidebar */}
            <div className="w-full lg:w-80 shrink-0">
              <div className="lg:sticky lg:top-24">
                <TabsList className="flex flex-row lg:flex-col h-auto bg-white/70 dark:bg-slate-900/70 backdrop-blur-xl border border-slate-200/50 dark:border-slate-800/50 p-2 space-y-0 lg:space-y-2 space-x-2 lg:space-x-0 rounded-2xl shadow-xl overflow-x-auto lg:overflow-x-visible">
                  {/* Profile Tab */}
                  <TabsTrigger
                    value="profile"
                    className="w-full lg:w-auto flex-shrink-0 justify-start data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-600 data-[state=active]:text-white rounded-xl py-3 lg:py-4 px-3 lg:px-4 transition-all duration-300 hover:bg-slate-100 dark:hover:bg-slate-800 group"
                  >
                    <div className="flex items-center gap-2 lg:gap-3 w-full">
                      <div className="p-1.5 lg:p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 group-data-[state=active]:bg-white/20 transition-colors">
                        <User className="h-4 w-4 text-blue-600 dark:text-blue-400 group-data-[state=active]:text-white" />
                      </div>
                      <div className="text-left hidden sm:block lg:block">
                        <div className="font-medium">Profile</div>
                        <div className="text-xs opacity-70">Personal information</div>
                      </div>
                      <div className="text-left sm:hidden">
                        <div className="font-medium text-sm">Profile</div>
                      </div>
                    </div>
                  </TabsTrigger>

                  {/* Account Tab */}
                  <TabsTrigger
                    value="account"
                    className="w-full justify-start data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-xl py-4 px-4 transition-all duration-300 hover:bg-slate-100 dark:hover:bg-slate-800 group"
                  >
                    <div className="flex items-center gap-3 w-full">
                      <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30 group-data-[state=active]:bg-white/20 transition-colors">
                        <Shield className="h-4 w-4 text-green-600 dark:text-green-400 group-data-[state=active]:text-white" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium">Account</div>
                        <div className="text-xs opacity-70">Security & login</div>
                      </div>
                    </div>
                  </TabsTrigger>

                  {/* Productivity Tab */}
                  <TabsTrigger
                    value="productivity"
                    className="w-full justify-start data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-violet-600 data-[state=active]:text-white rounded-xl py-4 px-4 transition-all duration-300 hover:bg-slate-100 dark:hover:bg-slate-800 group"
                  >
                    <div className="flex items-center gap-3 w-full">
                      <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30 group-data-[state=active]:bg-white/20 transition-colors">
                        <Timer className="h-4 w-4 text-purple-600 dark:text-purple-400 group-data-[state=active]:text-white" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium">Productivity</div>
                        <div className="text-xs opacity-70">Timer & features</div>
                      </div>
                    </div>
                  </TabsTrigger>

                  {/* Appearance Tab */}
                  <TabsTrigger
                    value="appearance"
                    className="w-full justify-start data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-amber-600 data-[state=active]:text-white rounded-xl py-4 px-4 transition-all duration-300 hover:bg-slate-100 dark:hover:bg-slate-800 group"
                  >
                    <div className="flex items-center gap-3 w-full">
                      <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/30 group-data-[state=active]:bg-white/20 transition-colors">
                        <Palette className="h-4 w-4 text-orange-600 dark:text-orange-400 group-data-[state=active]:text-white" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium">Appearance</div>
                        <div className="text-xs opacity-70">Theme & display</div>
                      </div>
                    </div>
                  </TabsTrigger>

                  {/* Data Tab */}
                  <TabsTrigger
                    value="data"
                    className="w-full justify-start data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-500 data-[state=active]:to-rose-600 data-[state=active]:text-white rounded-xl py-4 px-4 transition-all duration-300 hover:bg-slate-100 dark:hover:bg-slate-800 group"
                  >
                    <div className="flex items-center gap-3 w-full">
                      <div className="p-2 rounded-lg bg-red-100 dark:bg-red-900/30 group-data-[state=active]:bg-white/20 transition-colors">
                        <Activity className="h-4 w-4 text-red-600 dark:text-red-400 group-data-[state=active]:text-white" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium">Data</div>
                        <div className="text-xs opacity-70">Privacy & storage</div>
                      </div>
                    </div>
                  </TabsTrigger>
                </TabsList>

                {/* Share Section */}
                <Card className="mt-6 overflow-hidden border-0 shadow-xl bg-gradient-to-br from-indigo-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm rounded-2xl">
                  <CardHeader className="p-4 pb-2">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-xl bg-gradient-to-br from-pink-500 to-rose-600 shadow-lg animate-float">
                        <Share2 className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-base font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                          Share IsotopeAI
                        </CardTitle>
                        <CardDescription className="text-xs">Help friends excel in studies!</CardDescription>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="p-4 pt-2 space-y-3">
                    <Button
                      size="sm"
                      className="w-full bg-gradient-to-r from-[#25D366] to-[#128C7E] hover:opacity-90 hover:shadow-lg text-white gap-2 h-10 rounded-xl shadow-md transition-all duration-200 hover:scale-105"
                      onClick={() => {
                        const encodedMessage = encodeURIComponent(`Hey!\n\nCheck out *IsotopeAI* - a FREE productivity platform for students:\n\n• AI-powered study assistant\n• Pomodoro timer & study tracker\n• Daily analytics & progress charts\n• Task management system\n• Groups to study together\n• Mock Tests Analysis\n\nIt's helping me stay organized and productive.\n\nhttps://isotopeai.in`);
                        window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
                      }}
                    >
                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                        <path fillRule="evenodd" clipRule="evenodd" d="M17.415 14.382c-.298-.149-1.759-.867-2.031-.967-.272-.099-.47-.148-.669.15-.198.296-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.019-.458.13-.606.134-.133.297-.347.446-.52.149-.174.198-.298.297-.497.1-.198.05-.371-.025-.52-.074-.149-.668-1.612-.916-2.207-.241-.579-.486-.5-.668-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.064 2.875 1.213 ************* 2.095 3.2 5.076 4.487.71.306 1.263.489 1.694.625.712.227 1.36.195 1.871.118.57-.085 1.758-.719 2.006-1.413.247-.694.247-1.289.173-1.413-.074-.124-.272-.198-.57-.347z" />
                        <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 100-16 8 8 0 000 16z" />
                      </svg>
                      Share on WhatsApp
                    </Button>

                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        size="sm"
                        className="gap-1.5 h-9 rounded-xl shadow-md bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#FCAF45] hover:opacity-90 hover:shadow-lg text-white transition-all duration-200 hover:scale-105"
                        onClick={() => window.open('https://www.instagram.com/isotope.ai/', '_blank')}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect>
                          <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                          <line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line>
                        </svg>
                        Instagram
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="gap-1.5 h-9 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
                        onClick={() => {
                          navigator.clipboard.writeText('https://isotopeai.in');
                          toast({ title: "Link Copied!", description: "Share link copied to clipboard." });
                        }}
                      >
                        <Copy className="h-3.5 w-3.5" />
                        Copy
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Content Area */}
            <div className="flex-1">
              <TabsContent value="profile" className="m-0">
                <div className="space-y-8">
                  <div className="text-center">
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                      Your Profile
                    </h2>
                    <p className="text-slate-600 dark:text-slate-400">Manage your personal information and preferences</p>
                  </div>

                  {/* User Profile Card */}
                  <Card className="card-hover overflow-hidden border-0 shadow-xl bg-gradient-to-br from-blue-50 via-indigo-50/50 to-purple-50/30 dark:from-blue-950/50 dark:via-indigo-950/30 dark:to-purple-950/20 backdrop-blur-sm rounded-2xl">
                    <CardHeader className="p-6 pb-4">
                      <div className="flex items-center gap-4">
                        <div className="relative">
                          <Avatar className="h-20 w-20 ring-4 ring-white/50 dark:ring-slate-800/50 shadow-xl">
                            <AvatarImage src={user?.user_metadata?.avatar_url} alt="Profile" />
                            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-xl font-bold">
                              {user?.email?.charAt(0).toUpperCase() || 'U'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="absolute -bottom-1 -right-1 p-1.5 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full shadow-lg">
                            <div className="w-3 h-3 bg-white rounded-full"></div>
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                            {username || 'Anonymous User'}
                          </h3>
                          <p className="text-slate-600 dark:text-slate-400 flex items-center gap-2 mt-1">
                            <Mail className="h-4 w-4" />
                            {user?.email}
                          </p>
                          <div className="flex items-center gap-2 mt-2">
                            <div className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                              <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Active Student</span>
                            </div>
                            <div className="px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full">
                              <span className="text-xs font-medium text-green-700 dark:text-green-300">Verified</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                  </Card>

                  {/* Username Editor Card */}
                  <Card className="card-hover overflow-hidden border-0 shadow-xl bg-gradient-to-br from-indigo-50 via-blue-50/50 to-cyan-50/30 dark:from-indigo-950/50 dark:via-blue-950/30 dark:to-cyan-950/20 backdrop-blur-sm rounded-2xl">
                    <CardHeader className="p-6 pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-indigo-500 to-blue-600 shadow-lg">
                          <User className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                            Username Settings
                          </CardTitle>
                          <CardDescription>Customize how others see you on IsotopeAI</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-6 pt-2">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="username" className="text-sm font-medium flex items-center gap-2">
                            <Sparkles className="h-4 w-4 text-indigo-500" />
                            Display Name
                          </Label>
                          <Input
                            id="username"
                            type="text"
                            placeholder="Enter your username"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            disabled={isUpdating}
                            className="bg-white/70 dark:bg-slate-900/70 border-slate-200 dark:border-slate-700 focus-visible:ring-indigo-500/30 transition-all duration-200 h-12 rounded-xl"
                          />
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            This is how your name will appear to other users and in leaderboards.
                          </p>
                        </div>
                        <Button
                          onClick={handleUsernameUpdate}
                          disabled={isUpdating || !username.trim() || username === (user?.user_metadata?.username || '')}
                          className="w-full bg-gradient-to-r from-indigo-500 to-blue-600 hover:from-indigo-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 h-12 rounded-xl font-medium"
                        >
                          {isUpdating ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              Updating...
                            </>
                          ) : (
                            <>
                              <Star className="h-4 w-4 mr-2" />
                              Save Username
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Study Statistics Card */}
                  {stats && (
                    <Card className="card-hover overflow-hidden border-0 shadow-xl bg-gradient-to-br from-purple-50 via-pink-50/50 to-rose-50/30 dark:from-purple-950/50 dark:via-pink-950/30 dark:to-rose-950/20 backdrop-blur-sm rounded-2xl">
                      <CardHeader className="p-6 pb-4">
                        <div className="flex items-center gap-3">
                          <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 shadow-lg">
                            <Activity className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                              Study Statistics
                            </CardTitle>
                            <CardDescription>Your learning journey at a glance</CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6 pt-2">
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
                          <div className="text-center p-4 bg-white/70 dark:bg-slate-900/70 rounded-xl border border-slate-200/50 dark:border-slate-700/50">
                            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{stats.totalSessions}</div>
                            <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">Total Sessions</div>
                          </div>
                          <div className="text-center p-4 bg-white/70 dark:bg-slate-900/70 rounded-xl border border-slate-200/50 dark:border-slate-700/50">
                            <div className="text-2xl font-bold text-pink-600 dark:text-pink-400">{Math.round(stats.totalMinutes / 60)}h</div>
                            <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">Study Time</div>
                          </div>
                          <div className="text-center p-4 bg-white/70 dark:bg-slate-900/70 rounded-xl border border-slate-200/50 dark:border-slate-700/50">
                            <div className="text-2xl font-bold text-rose-600 dark:text-rose-400">{stats.currentStreak}</div>
                            <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">Current Streak</div>
                          </div>
                          <div className="text-center p-4 bg-white/70 dark:bg-slate-900/70 rounded-xl border border-slate-200/50 dark:border-slate-700/50">
                            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{stats.longestStreak}</div>
                            <div className="text-xs text-slate-600 dark:text-slate-400 mt-1">Best Streak</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              {/* Account Section */}
              <TabsContent value="account" className="m-0">
                <div className="space-y-8">
                  <div className="text-center">
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-2">
                      Account Security
                    </h2>
                    <p className="text-slate-600 dark:text-slate-400">Manage your login methods and account security</p>
                  </div>

                  {/* Current Login Methods Card */}
                  <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-green-50 via-emerald-50/50 to-teal-50/30 dark:from-green-950/50 dark:via-emerald-950/30 dark:to-teal-950/20 backdrop-blur-sm rounded-2xl">
                    <CardHeader className="p-6 pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 shadow-lg">
                          <Shield className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                            Login Methods
                          </CardTitle>
                          <CardDescription>Connected accounts for signing in</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-6 pt-2">
                      <div className="space-y-3">
                        {Array.isArray(providers) && providers.includes('password') && (
                          <div className="flex items-center justify-between p-4 rounded-xl bg-white/70 dark:bg-slate-900/70 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-md transition-all duration-200">
                            <div className="flex items-center gap-3">
                              <div className="p-2.5 rounded-lg bg-blue-100 dark:bg-blue-900/30 shadow-sm">
                                <Mail className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                              </div>
                              <div>
                                <p className="font-medium text-slate-900 dark:text-slate-100">Email & Password</p>
                                <p className="text-sm text-slate-600 dark:text-slate-400">{user?.email}</p>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleUnlinkProvider('password')}
                              disabled={linkingLoading || !Array.isArray(providers) || providers.length <= 1}
                              className="h-9 w-9 rounded-full text-slate-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-950/30 transition-colors"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}

                        {Array.isArray(providers) && providers.includes('google.com') && (
                          <div className="flex items-center justify-between p-4 rounded-xl bg-white/70 dark:bg-slate-900/70 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-md transition-all duration-200">
                            <div className="flex items-center gap-3">
                              <div className="p-2.5 rounded-lg bg-red-100 dark:bg-red-900/30 shadow-sm">
                                <svg className="h-5 w-5" viewBox="0 0 24 24">
                                  <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                                    <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z" />
                                    <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z" />
                                    <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z" />
                                    <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z" />
                                  </g>
                                </svg>
                              </div>
                              <div>
                                <p className="font-medium text-slate-900 dark:text-slate-100">Google Account</p>
                                <p className="text-sm text-slate-600 dark:text-slate-400">{user?.email}</p>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleUnlinkProvider('google.com')}
                              disabled={linkingLoading || !Array.isArray(providers) || providers.length <= 1}
                              className="h-9 w-9 rounded-full text-slate-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-950/30 transition-colors"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}

                        {Array.isArray(providers) && providers.length === 0 && (
                          <Alert variant="destructive" className="border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/30">
                            <AlertTitle className="font-medium">No login methods found</AlertTitle>
                            <AlertDescription className="text-sm">
                              This is unusual. Please contact support.
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Link with Email Card */}
                  {Array.isArray(providers) && !providers.includes('password') && !showExistingEmailForm && (
                    <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-emerald-50 via-green-50/50 to-lime-50/30 dark:from-emerald-950/50 dark:via-green-950/30 dark:to-lime-950/20 backdrop-blur-sm rounded-2xl">
                      <CardHeader className="p-6 pb-4">
                        <div className="flex items-center gap-3">
                          <div className="p-3 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-lg">
                            <Mail className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                              Add Email & Password
                            </CardTitle>
                            <CardDescription>Create a password for your account</CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6 pt-2">
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="linking-password" className="text-sm font-medium flex items-center gap-2">
                              <Lock className="h-4 w-4 text-emerald-500" />
                              Create Password
                            </Label>
                            <Input
                              id="linking-password"
                              type="password"
                              placeholder="Enter a secure password (min. 6 characters)"
                              value={linkingPassword}
                              onChange={(e) => setLinkingPassword(e.target.value)}
                              disabled={linkingLoading}
                              className="bg-white/70 dark:bg-slate-900/70 border-slate-200 dark:border-slate-700 focus-visible:ring-emerald-500/30 transition-all duration-200 h-12 rounded-xl"
                            />
                          </div>
                          {linkingError && !showExistingEmailForm && (
                            <Alert variant="destructive" className="border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/30">
                              <AlertDescription className="text-sm">{linkingError}</AlertDescription>
                            </Alert>
                          )}
                          <Button
                            onClick={handleLinkWithEmail}
                            disabled={linkingLoading || !linkingPassword || linkingPassword.length < 6}
                            className="w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 h-12 rounded-xl font-medium"
                          >
                            {linkingLoading ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Linking...
                              </>
                            ) : (
                              <>
                                <Mail className="h-4 w-4 mr-2" />
                                Link Email & Password
                              </>
                            )}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              {/* Productivity Section */}
              <TabsContent value="productivity" className="m-0">
                <div className="space-y-8">
                  <div className="text-center">
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent mb-2">
                      Productivity Settings
                    </h2>
                    <p className="text-slate-600 dark:text-slate-400">Customize your study timer and productivity features</p>
                  </div>

                  {/* Timer Settings Card */}
                  <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-purple-50 via-violet-50/50 to-indigo-50/30 dark:from-purple-950/50 dark:via-violet-950/30 dark:to-indigo-950/20 backdrop-blur-sm rounded-2xl">
                    <CardHeader className="p-6 pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-violet-600 shadow-lg">
                          <Timer className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                            Pomodoro Timer
                          </CardTitle>
                          <CardDescription>Configure your study and break intervals</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-6 pt-2">
                      <div className="grid gap-4 sm:gap-6 sm:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor="workDuration" className="text-sm font-medium flex items-center gap-2">
                            <AlarmClock className="h-4 w-4 text-purple-500" />
                            Work Duration (minutes)
                          </Label>
                          <Input
                            id="workDuration"
                            type="number"
                            min="1"
                            value={Math.floor(timerSettings.workDuration / 60)}
                            onChange={(e) => {
                              const minutes = parseInt(e.target.value);
                              if (!isNaN(minutes) && minutes > 0) {
                                updateTimerSettings({ workDuration: minutes * 60 });
                              }
                            }}
                            className="bg-white/70 dark:bg-slate-900/70 border-slate-200 dark:border-slate-700 focus-visible:ring-purple-500/30 transition-all duration-200 h-12 rounded-xl"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="shortBreakDuration" className="text-sm font-medium flex items-center gap-2">
                            <AlarmClock className="h-4 w-4 text-violet-500" />
                            Short Break (minutes)
                          </Label>
                          <Input
                            id="shortBreakDuration"
                            type="number"
                            min="1"
                            value={Math.floor(timerSettings.shortBreakDuration / 60)}
                            onChange={(e) => {
                              const minutes = parseInt(e.target.value);
                              if (!isNaN(minutes) && minutes > 0) {
                                updateTimerSettings({ shortBreakDuration: minutes * 60 });
                              }
                            }}
                            className="bg-white/70 dark:bg-slate-900/70 border-slate-200 dark:border-slate-700 focus-visible:ring-violet-500/30 transition-all duration-200 h-12 rounded-xl"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="longBreakDuration" className="text-sm font-medium flex items-center gap-2">
                            <AlarmClock className="h-4 w-4 text-indigo-500" />
                            Long Break (minutes)
                          </Label>
                          <Input
                            id="longBreakDuration"
                            type="number"
                            min="1"
                            value={Math.floor(timerSettings.longBreakDuration / 60)}
                            onChange={(e) => {
                              const minutes = parseInt(e.target.value);
                              if (!isNaN(minutes) && minutes > 0) {
                                updateTimerSettings({ longBreakDuration: minutes * 60 });
                              }
                            }}
                            className="bg-white/70 dark:bg-slate-900/70 border-slate-200 dark:border-slate-700 focus-visible:ring-indigo-500/30 transition-all duration-200 h-12 rounded-xl"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="sessionsUntilLongBreak" className="text-sm font-medium flex items-center gap-2">
                            <Zap className="h-4 w-4 text-pink-500" />
                            Sessions Until Long Break
                          </Label>
                          <Input
                            id="sessionsUntilLongBreak"
                            type="number"
                            min="1"
                            value={timerSettings.sessionsUntilLongBreak}
                            onChange={(e) => {
                              const sessions = parseInt(e.target.value);
                              if (!isNaN(sessions) && sessions > 0) {
                                updateTimerSettings({ sessionsUntilLongBreak: sessions });
                              }
                            }}
                            className="bg-white/70 dark:bg-slate-900/70 border-slate-200 dark:border-slate-700 focus-visible:ring-pink-500/30 transition-all duration-200 h-12 rounded-xl"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Notifications Card */}
                  {('Notification' in window) && (
                    <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-violet-50 via-purple-50/50 to-fuchsia-50/30 dark:from-violet-950/50 dark:via-purple-950/30 dark:to-fuchsia-950/20 backdrop-blur-sm rounded-2xl">
                      <CardHeader className="p-6 pb-4">
                        <div className="flex items-center gap-3">
                          <div className="p-3 rounded-xl bg-gradient-to-br from-violet-500 to-purple-600 shadow-lg">
                            <Bell className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                              Browser Notifications
                            </CardTitle>
                            <CardDescription>Get notified when timer sessions complete</CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6 pt-2">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 rounded-xl bg-white/70 dark:bg-slate-900/70 border border-slate-200/50 dark:border-slate-700/50">
                          <div>
                            <h4 className="text-sm font-medium flex items-center gap-2">
                              <Bell className={`h-4 w-4 ${notificationPermission === 'granted' ? "text-green-500" : "text-slate-400"}`} />
                              Notification Status
                            </h4>
                            <p className="text-xs text-slate-600 dark:text-slate-400 mt-1">
                              {notificationPermission === 'granted'
                                ? "Notifications are enabled for timer alerts and reminders"
                                : notificationPermission === 'denied'
                                ? "Notifications are blocked in your browser settings"
                                : "Allow notifications for timer alerts and reminders"}
                            </p>
                          </div>
                          <Button
                            onClick={requestNotificationPermission}
                            variant="outline"
                            size="sm"
                            disabled={notificationPermission === 'denied'}
                            className={notificationPermission === 'granted'
                              ? "bg-green-50 dark:bg-green-950/30 text-green-600 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-950/50 border-green-200 dark:border-green-800 transition-all duration-200"
                              : "transition-all duration-200 hover:border-purple-300 hover:bg-purple-50 dark:hover:bg-purple-950/30 hover:text-purple-600"}
                          >
                            {notificationPermission === 'granted' ? (
                              <>
                                <Bell className="h-4 w-4 mr-2" />
                                Enabled
                              </>
                            ) : (
                              <>
                                {notificationPermission === 'denied' ? "Blocked by Browser" : "Enable Notifications"}
                              </>
                            )}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Component Visibility Card */}
                  <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-fuchsia-50 via-pink-50/50 to-rose-50/30 dark:from-fuchsia-950/50 dark:via-pink-950/30 dark:to-rose-950/20 backdrop-blur-sm rounded-2xl">
                    <CardHeader className="p-6 pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-fuchsia-500 to-pink-600 shadow-lg">
                          <Eye className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                            Productivity Components
                          </CardTitle>
                          <CardDescription>Customize which features appear on your productivity page</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-6 pt-2">
                      <div className="grid gap-4 md:grid-cols-2">
                        {/* Motivational Quotes toggle */}
                        <div className="p-4 rounded-xl bg-white/70 dark:bg-slate-900/70 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-md transition-all duration-200">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-lg bg-amber-100 dark:bg-amber-900/30">
                                <svg className="h-4 w-4 text-amber-600 dark:text-amber-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z" />
                                  <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z" />
                                </svg>
                              </div>
                              <div>
                                <p className="font-medium text-slate-900 dark:text-slate-100">Motivational Quotes</p>
                                <p className="text-xs text-slate-600 dark:text-slate-400">Inspirational study quotes</p>
                              </div>
                            </div>
                            <Switch
                              checked={visibilitySettings.showQuotes}
                              onCheckedChange={(checked) => updateVisibilitySettings('showQuotes', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-amber-500"
                            />
                          </div>
                        </div>

                        {/* Tasks toggle */}
                        <div className="p-4 rounded-xl bg-white/70 dark:bg-slate-900/70 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-md transition-all duration-200">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                                <svg className="h-4 w-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <rect width="8" height="8" x="3" y="3" rx="1" />
                                  <path d="m7 7 3 3" />
                                  <path d="M7 10 10 7" />
                                  <rect width="8" height="8" x="13" y="3" rx="1" />
                                  <path d="m17 7 3 3" />
                                  <path d="M17 10 20 7" />
                                  <rect width="8" height="8" x="3" y="13" rx="1" />
                                  <path d="m7 17 3 3" />
                                  <path d="M7 20 10 17" />
                                  <rect width="8" height="8" x="13" y="13" rx="1" />
                                  <path d="m17 17 3 3" />
                                  <path d="M17 20 20 17" />
                                </svg>
                              </div>
                              <div>
                                <p className="font-medium text-slate-900 dark:text-slate-100">Tasks Menu</p>
                                <p className="text-xs text-slate-600 dark:text-slate-400">To-do list management</p>
                              </div>
                            </div>
                            <Switch
                              checked={visibilitySettings.showTasks}
                              onCheckedChange={(checked) => updateVisibilitySettings('showTasks', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-blue-500"
                            />
                          </div>
                        </div>

                        {/* Exam Countdown toggle */}
                        <div className="p-4 rounded-xl bg-white/70 dark:bg-slate-900/70 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-md transition-all duration-200">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-lg bg-red-100 dark:bg-red-900/30">
                                <AlarmClock className="h-4 w-4 text-red-600 dark:text-red-400" />
                              </div>
                              <div>
                                <p className="font-medium text-slate-900 dark:text-slate-100">Exam Countdown</p>
                                <p className="text-xs text-slate-600 dark:text-slate-400">Upcoming exam timers</p>
                              </div>
                            </div>
                            <Switch
                              checked={visibilitySettings.showExamCountdown}
                              onCheckedChange={(checked) => updateVisibilitySettings('showExamCountdown', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-red-500"
                            />
                          </div>
                        </div>

                        {/* Spotify toggle */}
                        <div className="p-4 rounded-xl bg-white/70 dark:bg-slate-900/70 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-md transition-all duration-200">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30">
                                <svg className="h-4 w-4 text-green-600 dark:text-green-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <circle cx="12" cy="12" r="10" />
                                  <path d="M8 12.5a5 5 0 0 1 8 0" />
                                  <path d="M9.5 10a3 3 0 0 1 5 0" />
                                  <path d="M11 8a1 1 0 0 1 2 0" />
                                </svg>
                              </div>
                              <div>
                                <p className="font-medium text-slate-900 dark:text-slate-100">Spotify Player</p>
                                <p className="text-xs text-slate-600 dark:text-slate-400">Music for studying</p>
                              </div>
                            </div>
                            <Switch
                              checked={visibilitySettings.showSpotifyBar}
                              onCheckedChange={(checked) => updateVisibilitySettings('showSpotifyBar', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-green-500"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Spotify collapsed state toggle */}
                      {visibilitySettings.showSpotifyBar && (
                        <div className="mt-4 p-4 rounded-xl bg-green-50/50 dark:bg-green-950/20 border border-green-200/50 dark:border-green-800/50">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30">
                                <EyeOff className="h-4 w-4 text-green-600 dark:text-green-400" />
                              </div>
                              <div>
                                <p className="font-medium text-slate-900 dark:text-slate-100">Start Collapsed</p>
                                <p className="text-xs text-slate-600 dark:text-slate-400">Begin with Spotify player minimized</p>
                              </div>
                            </div>
                            <Switch
                              checked={visibilitySettings.spotifyCollapsed}
                              onCheckedChange={(checked) => updateVisibilitySettings('spotifyCollapsed', checked)}
                              disabled={isUpdating}
                              className="data-[state=checked]:bg-green-500"
                            />
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Appearance Section */}
              <TabsContent value="appearance" className="m-0">
                <div className="space-y-8">
                  <div className="text-center">
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent mb-2">
                      Appearance
                    </h2>
                    <p className="text-slate-600 dark:text-slate-400">Customize how the application looks and feels</p>
                  </div>

                  {/* Theme Selection Card */}
                  <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-orange-50 via-amber-50/50 to-yellow-50/30 dark:from-orange-950/50 dark:via-amber-950/30 dark:to-yellow-950/20 backdrop-blur-sm rounded-2xl">
                    <CardHeader className="p-6 pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500 to-amber-600 shadow-lg">
                          <Palette className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                            Theme Selection
                          </CardTitle>
                          <CardDescription>Choose your preferred color scheme</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-6 pt-2">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Light Theme */}
                        <div
                          className={`group cursor-pointer p-6 rounded-2xl border-2 transition-all duration-300 hover:shadow-lg ${theme === 'light'
                            ? 'bg-white border-orange-300 shadow-lg ring-4 ring-orange-200/50'
                            : 'bg-white/90 border-slate-200 hover:border-orange-200'}`}
                          onClick={() => theme !== 'light' && toggleTheme()}
                        >
                          <div className="flex flex-col items-center text-center space-y-4">
                            <div className="relative">
                              <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-amber-400 to-orange-500 flex items-center justify-center shadow-xl group-hover:scale-105 transition-transform">
                                <Sun className="h-10 w-10 text-white" />
                              </div>
                              {theme === 'light' && (
                                <div className="absolute -top-2 -right-2 p-1 bg-orange-500 rounded-full shadow-lg">
                                  <div className="w-3 h-3 bg-white rounded-full"></div>
                                </div>
                              )}
                            </div>
                            <div>
                              <h4 className="text-lg font-semibold text-slate-900 mb-1">Light Mode</h4>
                              <p className="text-sm text-slate-600">
                                Bright and clean interface perfect for daytime use
                              </p>
                            </div>
                            {theme === 'light' && (
                              <div className="px-4 py-2 bg-orange-100 text-orange-700 text-sm font-medium rounded-full">
                                Currently Active
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Dark Theme */}
                        <div
                          className={`group cursor-pointer p-6 rounded-2xl border-2 transition-all duration-300 hover:shadow-lg ${theme === 'dark'
                            ? 'bg-slate-900 border-orange-300 shadow-lg ring-4 ring-orange-200/50'
                            : 'bg-slate-900/90 border-slate-700 hover:border-orange-300'}`}
                          onClick={() => theme !== 'dark' && toggleTheme()}
                        >
                          <div className="flex flex-col items-center text-center space-y-4">
                            <div className="relative">
                              <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-indigo-600 to-purple-700 flex items-center justify-center shadow-xl group-hover:scale-105 transition-transform">
                                <Moon className="h-10 w-10 text-white" />
                              </div>
                              {theme === 'dark' && (
                                <div className="absolute -top-2 -right-2 p-1 bg-orange-500 rounded-full shadow-lg">
                                  <div className="w-3 h-3 bg-white rounded-full"></div>
                                </div>
                              )}
                            </div>
                            <div>
                              <h4 className="text-lg font-semibold text-slate-100 mb-1">Dark Mode</h4>
                              <p className="text-sm text-slate-400">
                                Elegant dark interface that reduces eye strain
                              </p>
                            </div>
                            {theme === 'dark' && (
                              <div className="px-4 py-2 bg-orange-900/30 text-orange-300 text-sm font-medium rounded-full">
                                Currently Active
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="mt-6 p-4 bg-amber-50/50 dark:bg-amber-950/20 rounded-xl border border-amber-200/50 dark:border-amber-800/50">
                        <p className="text-sm text-amber-800 dark:text-amber-200 text-center">
                          <Sparkles className="h-4 w-4 inline mr-2" />
                          Your theme preference is automatically saved and synced across all your devices
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Data Section */}
              <TabsContent value="data" className="m-0">
                <div className="space-y-8">
                  <div className="text-center">
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-red-600 to-rose-600 bg-clip-text text-transparent mb-2">
                      Data & Privacy
                    </h2>
                    <p className="text-slate-600 dark:text-slate-400">Manage your personal data and privacy settings</p>
                  </div>

                  {/* Chat Data Management Card */}
                  <Card className="overflow-hidden border-0 shadow-xl bg-gradient-to-br from-red-50 via-rose-50/50 to-pink-50/30 dark:from-red-950/50 dark:via-rose-950/30 dark:to-pink-950/20 backdrop-blur-sm rounded-2xl">
                    <CardHeader className="p-6 pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-red-500 to-rose-600 shadow-lg">
                          <MessageSquare className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                            Chat History
                          </CardTitle>
                          <CardDescription>Manage your AI conversation data</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-6 pt-2">
                      <div className="p-6 bg-white/70 dark:bg-slate-900/70 rounded-xl border border-slate-200/50 dark:border-slate-700/50">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                          <div className="space-y-3">
                            <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2">
                              <Trash2 className="h-5 w-5 text-red-500" />
                              Delete All Conversations
                            </h4>
                            <p className="text-sm text-slate-600 dark:text-slate-400">
                              This will permanently remove all your AI chat history from our servers. This action cannot be undone.
                            </p>
                            <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-950/30 rounded-lg border border-amber-200/50 dark:border-amber-800/50">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-600 dark:text-amber-400">
                                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                                <line x1="12" y1="9" x2="12" y2="13"></line>
                                <line x1="12" y1="17" x2="12.01" y2="17"></line>
                              </svg>
                              <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                                Warning: This action is irreversible
                              </span>
                            </div>
                          </div>

                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="destructive"
                                size="lg"
                                className="bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl font-medium"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete All Chats
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent className="max-w-md border-red-200 dark:border-red-800 rounded-2xl">
                              <AlertDialogHeader>
                                <AlertDialogTitle className="text-red-600 dark:text-red-400 flex items-center gap-2 text-xl">
                                  <Trash2 className="h-6 w-6" />
                                  Are you absolutely sure?
                                </AlertDialogTitle>
                                <AlertDialogDescription className="text-base">
                                  This action cannot be undone. This will permanently delete all your chat history and remove the data from our servers.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter className="gap-3">
                                <AlertDialogCancel className="rounded-xl transition-all duration-200">Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={handleDeleteChats}
                                  className="bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 rounded-xl transition-all duration-200"
                                  disabled={isUpdating}
                                >
                                  {isUpdating ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Deleting...
                                    </>
                                  ) : (
                                    <>
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      Delete All Chats
                                    </>
                                  )}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>

                      <div className="mt-4 p-4 bg-blue-50/50 dark:bg-blue-950/20 rounded-xl border border-blue-200/50 dark:border-blue-800/50">
                        <p className="text-sm text-blue-800 dark:text-blue-200 text-center flex items-center justify-center gap-2">
                          <Lock className="h-4 w-4" />
                          Your data is securely encrypted and never shared with third parties
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </div>
          </div>
        </Tabs>
      </div>
    </div>
  </>
  );
};

export default ModernSettingsPage;
